所有终端输出已重定向到: ../model/ACDC_temporal_ddnet_DD-Net-SAM2-adapter-EMA-Switch-20000_3_labeled/all_output.txt
====== 日志系统配置完成 - 包含文件输出 ======
依赖安装完成
[20:53:51.760] 未找到适配器特定权重文件，将使用标准权重并初始化适配器参数
[20:53:51.761] ============= 训练开始 =============
[20:53:51.761] 日志文件保存在: ../model/ACDC_temporal_ddnet_DD-Net-SAM2-adapter-EMA-Switch-20000_3_labeled/log.txt
[20:53:51.761] Namespace(root_path='../data/ACDC', exp='DD-Net-SAM2-adapter-EMA-Switch-20000', model='temporal_ddnet', max_iterations=30000, batch_size=24, deterministic=1, base_lr=0.01, patch_size=[256, 256], seed=1337, num_classes=4, labeled_bs=12, labeled_num=3, gpu='1', consistency=0.1, ema_decay=0.99, consistency_rampup=200.0, temperature=0.2, lamda=1, beta=0.3, temp=1, sam2_model_id='base', sam2_weight='/mnt/sda/zzh/DD-Net/sam2/checkpoints/sam2.1_hiera_base_plus.pt', sam2_weight_loss=0.2, sam2_confidence=0.8, sam2_frequency=1, use_ordered_slices=0, use_adapter=1, adapter_scale_factor=32, adapter_img_size=256, adapter_patch_size=16, teacher_switch_iter=20000, use_ema_teacher=1)
[20:53:51.761] Loading SAM2 model from local file: /mnt/sda/zzh/DD-Net/sam2/checkpoints/sam2.1_hiera_base_plus.pt
【PRINT输出】SAM2适配器成功加载！
【PRINT输出】适配器参数: scale_factor=32, embed_dim=256
设置SAM2适配器为训练模式...
【设置模块为训练模式】: prompt_generator
【设置模块为训练模式】: prompt_generator.shared_mlp
【设置模块为训练模式】: prompt_generator.embedding_generators
【设置模块为训练模式】: prompt_generator.embedding_generators.0
【设置模块为训练模式】: prompt_generator.embedding_generators.1
【设置模块为训练模式】: prompt_generator.embedding_generators.2
【设置模块为训练模式】: prompt_generator.embedding_generators.3
【设置模块为训练模式】: prompt_generator.lightweight_mlps
【设置模块为训练模式】: prompt_generator.lightweight_mlps.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.0.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.0.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.1.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.1.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.2
【设置模块为训练模式】: prompt_generator.lightweight_mlps.2.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.2.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.3
【设置模块为训练模式】: prompt_generator.lightweight_mlps.3.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.3.1
【设置模块为训练模式】: prompt_generator.prompt_generator
【设置模块为训练模式】: prompt_generator.prompt_generator.0
【设置模块为训练模式】: prompt_generator.prompt_generator.1
【设置模块为训练模式】: prompt_generator.prompt_generator.2
【设置模块为训练模式】: prompt_generator.prompt_generator.3
SAM2适配器参数已设置为可训练
[20:53:52.650] SAM2 model loaded successfully
创建网络模型 - 类型: temporal_ddnet, 输入通道: 1, 输出类别数: 4
创建网络模型 - 类型: temporal_ddnet, 输入通道: 1, 输出类别数: 4
[20:53:52.699] EMA教师模型已创建
[20:53:52.699] Using random slices dataset
总计 1312 个样本
总计 20 个样本
Total silices is: 1312, labeled slices is: 68
【调试】适配器启用: 1, SAM2模型已加载
【调试】SAM2模型类型: <class '__main__.SilentSAM2ImagePredictor'>
【调试】是否有sam_model属性: True
【调试】检查sam_model的参数名称:
【调试】找到适配器参数: image_encoder.prompt_generator.shared_mlp.weight
【调试】找到适配器参数: image_encoder.prompt_generator.shared_mlp.bias
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.0.weight
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.0.bias
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.1.weight
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.1.bias
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.2.weight
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.2.bias
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.3.weight
【调试】找到适配器参数: image_encoder.prompt_generator.embedding_generators.3.bias
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.0.0.weight
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.0.0.bias
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.1.0.weight
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.1.0.bias
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.2.0.weight
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.2.0.bias
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.3.0.weight
【调试】找到适配器参数: image_encoder.prompt_generator.lightweight_mlps.3.0.bias
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.0.weight
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.0.bias
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.1.weight
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.1.bias
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.2.weight
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.2.bias
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.3.weight
【调试】找到适配器参数: image_encoder.prompt_generator.prompt_generator.3.bias
【调试】可训练的适配器参数数量: 26
[20:53:52.710] 创建SAM2适配器优化器，参数数量: 26
【调试】成功创建SAM2适配器优化器
[20:53:52.711] 5 iterations per epoch
[20:53:53.579] 当前使用EMA作为教师模型 (迭代 0 < 20000)
[20:53:53.610] EMA教师损失: 1.371806025505066
[20:53:53.692] SAM2适配器训练成功，平均损失: 1.3381288051605225
[20:53:53.915] iteration 1 : loss : 2.575043, supervised_loss: 1.610687, loss_deep_sup: 0.815963, pseudo_supervision: 2.772404, loss_kd: 0.001063, low_loss: 0.072270, teacher_loss: 1.371806, teacher_type: EMA, ema_loss: 1.371806, sam2_loss: 0.000000
[20:53:54.211] iteration 2 : loss : 2.514313, supervised_loss: 1.580413, loss_deep_sup: 0.806593, pseudo_supervision: 2.772515, loss_kd: 0.000372, low_loss: 0.062107, teacher_loss: 1.267718, teacher_type: EMA, ema_loss: 1.267718, sam2_loss: 0.000000
[20:53:54.512] iteration 3 : loss : 2.542166, supervised_loss: 1.606785, loss_deep_sup: 0.818851, pseudo_supervision: 2.772316, loss_kd: 0.000457, low_loss: 0.056682, teacher_loss: 1.248473, teacher_type: EMA, ema_loss: 1.248473, sam2_loss: 0.000000
[20:53:54.807] iteration 4 : loss : 2.447680, supervised_loss: 1.565060, loss_deep_sup: 0.806560, pseudo_supervision: 2.772211, loss_kd: 0.000285, low_loss: 0.036530, teacher_loss: 1.256803, teacher_type: EMA, ema_loss: 1.256803, sam2_loss: 0.000000
[20:53:55.109] iteration 5 : loss : 2.434360, supervised_loss: 1.548281, loss_deep_sup: 0.798062, pseudo_supervision: 2.770586, loss_kd: 0.000531, low_loss: 0.042400, teacher_loss: 1.216613, teacher_type: EMA, ema_loss: 1.216613, sam2_loss: 0.000000
[20:53:55.958] iteration 6 : loss : 2.401133, supervised_loss: 1.523246, loss_deep_sup: 0.793859, pseudo_supervision: 2.768373, loss_kd: 0.000788, low_loss: 0.040284, teacher_loss: 1.195526, teacher_type: EMA, ema_loss: 1.195526, sam2_loss: 0.000000
[20:53:56.255] iteration 7 : loss : 2.378872, supervised_loss: 1.511521, loss_deep_sup: 0.796007, pseudo_supervision: 2.761560, loss_kd: 0.001107, low_loss: 0.033791, teacher_loss: 1.180040, teacher_type: EMA, ema_loss: 1.180040, sam2_loss: 0.000000
[20:53:56.558] iteration 8 : loss : 2.393642, supervised_loss: 1.527364, loss_deep_sup: 0.801439, pseudo_supervision: 2.751832, loss_kd: 0.001287, low_loss: 0.030465, teacher_loss: 1.138908, teacher_type: EMA, ema_loss: 1.138908, sam2_loss: 0.000000
[20:53:56.865] iteration 9 : loss : 2.410781, supervised_loss: 1.533107, loss_deep_sup: 0.814473, pseudo_supervision: 2.739829, loss_kd: 0.001355, low_loss: 0.029638, teacher_loss: 1.074881, teacher_type: EMA, ema_loss: 1.074881, sam2_loss: 0.000000
