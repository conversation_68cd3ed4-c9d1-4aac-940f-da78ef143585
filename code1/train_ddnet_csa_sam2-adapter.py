"""
    创建时间：2023/11/12 13:50
    修改时间：实现SAM2-adapter和EMA模型交替使用策略

    重要修改：实现方案1 - 基于固定迭代次数的交互点策略
    - 重新引入EMA教师模型，与SAM2-adapter协同工作
    - 前期阶段(0-20000次迭代)：EMA模型提供伪标签，SAM2-adapter参与训练但不提供伪标签
    - 后期阶段(20000次迭代后)：SAM2-adapter开始提供伪标签，EMA停止提供伪标签
    - 在整个训练过程中保持SAM2适配器的训练
    - 根据当前阶段动态选择教师模型和相应的损失函数
"""
# 移除这里的全局日志配置，避免与后面的配置冲突
import logging
import sys
# 不在开头设置基本配置，避免与后面冲突
# logging.basicConfig(level=logging.INFO)

# 添加Tee类，用于将所有输出同时重定向到文件和终端
class Tee:
    def __init__(self, file_path, mode='a'):
        self.file = open(file_path, mode)
        self.stdout = sys.stdout
        sys.stdout = self
        
    def __del__(self):
        sys.stdout = self.stdout
        if self.file:
            self.file.close()
    
    def write(self, data):
        self.file.write(data)
        self.stdout.write(data)
        self.file.flush()
        self.stdout.flush()
        
    def flush(self):
        self.file.flush()
        self.stdout.flush()

# 添加一个函数用于提前配置日志
def setup_early_logging(log_file=None):
    handlers = [logging.StreamHandler(sys.stdout)]
    
    # 如果提供了日志文件路径，也添加到文件的处理器
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    # 临时配置，输出到控制台和文件(如果提供)
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s.%(msecs)03d] %(message)s', 
        datefmt='%H:%M:%S',
        handlers=handlers
    )
    print(f"====== 日志系统配置完成 - {'包含文件输出' if log_file else '仅控制台'} ======")

# 仅抑制特定库的日志，不影响主日志
import logging as python_logging
for logger_name in ["sam2", "matplotlib", "PIL", "torch", "numpy"]:
    logger = python_logging.getLogger(logger_name)
    logger.setLevel(python_logging.ERROR)
    logger.propagate = False

import argparse
import os
import random
import shutil
import numpy as np
import torch
import torch.backends.cudnn as cudnn
import torch.nn.functional as F
import torch.optim as optim
from tensorboardX import SummaryWriter
from torch.nn.modules.loss import CrossEntropyLoss,KLDivLoss,BCELoss
from torch.utils.data import DataLoader
from torchvision import transforms
from tqdm import tqdm
# 从数据集导入将在use_ordered_slices条件判断后进行
from networks.csa_factory import net_factory
from utils import losses, ramps, val

# 添加subprocess用于运行命令
import subprocess

# SAM2路径设置
sam2_path = '/mnt/sda/zzh/DD-Net/sam2'
# 确保SAM2路径在PYTHONPATH中
if sam2_path not in sys.path:
    sys.path.append(sam2_path)
    os.environ['PYTHONPATH'] = f"{os.environ.get('PYTHONPATH', '')}:{sam2_path}"

# 安装必要的依赖
def install_dependencies():
    try:
        subprocess.run(["pip", "install", "huggingface_hub", "--quiet"], check=True)
        print("依赖安装完成")
    except Exception as e:
        print(f"安装依赖时出错: {e}")

# 检查SAM2模型权重文件并下载（如需要）
def check_sam2_weights(model_id, use_adapter=False):
    # 根据模型ID确定权重文件名
    if "small" in model_id:
        weight_file = "sam2.1_hiera_small.pt"
    elif "base" in model_id:
        weight_file = "sam2.1_hiera_base_plus.pt"
    elif "large" in model_id:
        weight_file = "sam2.1_hiera_l.pt"
    else:  # tiny作为默认
        weight_file = "sam2.1_hiera_tiny.pt"
    
    # 如果使用适配器，检查适配器权重文件
    adapter_suffix = "_adapter" if use_adapter else ""
    adapter_weight_file = weight_file.replace(".pt", f"{adapter_suffix}.pt")
    
    # 首先检查是否有适配器特定权重文件
    if use_adapter:
        adapter_checkpoint_path = f"{sam2_path}/checkpoints/{adapter_weight_file}"
        if os.path.exists(adapter_checkpoint_path):
            logging.info(f"找到适配器特定权重文件: {adapter_checkpoint_path}")
            return adapter_checkpoint_path
        else:
            logging.info(f"未找到适配器特定权重文件，将使用标准权重并初始化适配器参数")
    
    # 检查标准权重文件
    checkpoint_path = f"{sam2_path}/checkpoints/{weight_file}"
    
    if not os.path.exists(checkpoint_path):
        print(f"SAM2模型权重文件不存在，尝试下载...")
        os.makedirs(f"{sam2_path}/checkpoints", exist_ok=True)
        current_dir = os.getcwd()
        os.chdir(f"{sam2_path}/checkpoints")
        subprocess.run(["sh", "download_ckpts.sh"], check=True)
        os.chdir(current_dir)
    
    return checkpoint_path

# SAM2 导入
from sam2.build_sam import build_sam2
from sam2.utils.transforms import SAM2Transforms
from sam2.sam2_image_predictor import SAM2ImagePredictor  # 导入预测器类

# 添加抑制调试日志输出
import logging as python_logging
# 抑制SAM2内部的调试日志
python_logging.getLogger("sam2").setLevel(python_logging.ERROR)  # 将WARNING改为ERROR
# 抑制matplotlib的调试日志
python_logging.getLogger("matplotlib").setLevel(python_logging.ERROR)  # 将WARNING改为ERROR
# 抑制PIL的调试日志
python_logging.getLogger("PIL").setLevel(python_logging.ERROR)  # 将WARNING改为ERROR

# 修改SAM2ImagePredictor类以抑制内部日志
class SilentSAM2ImagePredictor(SAM2ImagePredictor):
    """禁用所有调试输出的SAM2预测器"""
    
    def __init__(self, sam_model, *args, **kwargs):
        super().__init__(sam_model, *args, **kwargs)
        # 确保sam_model作为公开属性可访问
        self.sam_model = sam_model
    
    def set_image(self, image, *args, **kwargs):
        # 暂时禁用所有日志
        original_level = python_logging.root.level
        python_logging.root.setLevel(python_logging.CRITICAL)
        
        # 调用原始方法
        result = super().set_image(image, *args, **kwargs)
        
        # 恢复日志级别
        python_logging.root.setLevel(original_level)
        return result
    
    def predict(self, *args, **kwargs):
        # 暂时禁用所有日志
        original_level = python_logging.root.level
        python_logging.root.setLevel(python_logging.CRITICAL)
        
        # 调用原始方法
        result = super().predict(*args, **kwargs)
        
        # 恢复日志级别
        python_logging.root.setLevel(original_level)
        return result

def get_current_consistency_weight(epoch):
    # Consistency ramp-up from https://arxiv.org/abs/1610.02242
    return args.consistency * ramps.sigmoid_rampup(epoch, args.consistency_rampup)

parser = argparse.ArgumentParser()
parser.add_argument('--root_path', type=str, default='../data/ACDC', help='Name of Experiment')
parser.add_argument('--exp', type=str, default='DD-Net-SAM2-adapter-EMA-Switch-20000', help='experiment_name')
parser.add_argument('--model', type=str, default='temporal_ddnet', help='model_name')
parser.add_argument('--max_iterations', type=int, default=30000, help='maximum epoch number to train')
parser.add_argument('--batch_size', type=int, default=24, help='batch_size per gpu')
parser.add_argument('--deterministic', type=int, default=1, help='whether use deterministic training')
parser.add_argument('--base_lr', type=float, default=0.01, help='segmentation network learning rate')
parser.add_argument('--patch_size', type=list, default=[256, 256], help='patch size of network input')
parser.add_argument('--seed', type=int, default=1337, help='random seed')
parser.add_argument('--num_classes', type=int, default=4, help='output channel of network')
parser.add_argument('--labeled_bs', type=int, default=12, help='labeled_batch_size per gpu')
parser.add_argument('--labeled_num', type=int, default=3, help='labeled data')
parser.add_argument('--gpu', type=str, default='1', help='GPU to use')
parser.add_argument('--consistency', type=float, default=0.1, help='consistency')
parser.add_argument('--ema_decay', type=float,  default=0.99, help='ema_decay')
parser.add_argument('--consistency_rampup', type=float, default=200.0, help='consistency_rampup')
parser.add_argument('--temperature', type=float, default=0.2, help='temperature of sharpening')
parser.add_argument('--lamda', type=float, default=1, help='weight to balance all losses')
parser.add_argument('--beta', type=float,  default=0.3, help='balance factor to control regional and sdm loss')
parser.add_argument('--temp', default=1, type=float)

# SAM2 相关参数
parser.add_argument('--sam2_model_id', type=str, default='base', help='SAM2 model ID')
parser.add_argument('--sam2_weight', type=str, default='/mnt/sda/zzh/DD-Net/sam2/checkpoints/sam2.1_hiera_base_plus.pt', help='Path to SAM2 model weights')
parser.add_argument('--sam2_weight_loss', type=float, default=0.2, help='Weight for SAM2 loss')
parser.add_argument('--sam2_confidence', type=float, default=0.8, help='Confidence threshold for SAM2 pseudo labels')
parser.add_argument('--sam2_frequency', type=int, default=1, help='Frequency of SAM2 usage (every N iterations)')
parser.add_argument('--use_ordered_slices', type=int, default=0, help='Use ordered slices list (1) or random slices (0)')
# 添加SAM2适配器相关参数
parser.add_argument('--use_adapter', type=int, default=1, help='是否使用适配器模式 (1=使用, 0=不使用)')
parser.add_argument('--adapter_scale_factor', type=int, default=32, help='适配器缩放因子')
parser.add_argument('--adapter_img_size', type=int, default=256, help='适配器图像尺寸')
parser.add_argument('--adapter_patch_size', type=int, default=16, help='适配器补丁大小')
# 添加交互点相关参数
parser.add_argument('--teacher_switch_iter', type=int, default=20000, help='教师模型切换的迭代次数')
parser.add_argument('--use_ema_teacher', type=int, default=1, help='是否使用EMA教师模型 (1=使用, 0=不使用)')

args = parser.parse_args()

os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
def patients_to_slices(dataset, patiens_num):   # _,7
    ref_dict = None
    if "ACDC" in dataset:
        ref_dict = {"1": 34, "3": 68, "5": 92, "7": 136,
                    "14": 256, "21": 396, "28": 512, "35": 664, "70": 1312}
    elif "Prostate":
        ref_dict = {"2": 47, "4": 111, "7": 191,
                    "11": 306, "14": 391, "18": 478, "35": 940}
    else:
        print("Error")
    return ref_dict[str(patiens_num)]

def sharpening(P):
    T = 1/args.temperature
    P_sharpen = P ** T / (P ** T + (1-P) ** T)
    return P_sharpen

def dice1_loss(score, target):
    target = target.float()
    smooth = 1e-5
    intersect = torch.sum(score * target)
    y_sum = torch.sum(target * target)
    z_sum = torch.sum(score * score)
    loss = (2 * intersect + smooth) / (z_sum + y_sum + smooth)
    loss = 1 - loss
    return loss

def update_ema_variables(model, ema_model, alpha, global_step):
    # Use the true average until the exponential average is more correct
    alpha = min(1 - 1 / (global_step + 1), alpha)
    for ema_param, param in zip(ema_model.parameters(), model.parameters()):
        ema_param.data.mul_(alpha).add_(param.data, alpha=1 - alpha)

# 从高置信度预测生成边界框作为SAM2的提示
def generate_bbox_from_pred(pred_mask, confidence_threshold=0.75):
    """从预测掩码生成边界框，作为SAM2的提示"""
    batch_size, num_classes, h, w = pred_mask.shape
    bboxes = []
    
    # 设置日志级别为ERROR以抑制调试输出
    original_level = python_logging.root.level
    python_logging.root.setLevel(python_logging.ERROR)
    
    for b in range(batch_size):
        sample_bboxes = []
        for c in range(1, num_classes):  # 跳过背景类
            # 获取当前类别的预测掩码
            mask = pred_mask[b, c] > confidence_threshold
            # 检查是否有足够的像素超过阈值 - 至少区域总面积的1%
            min_area = 0.01 * h * w
            if mask.sum() > min_area:
                # 获取非零像素的坐标
                y_indices, x_indices = torch.where(mask)
                if len(y_indices) > 0 and len(x_indices) > 0:
                    # 计算边界框坐标
                    x_min, x_max = x_indices.min().item(), x_indices.max().item()
                    y_min, y_max = y_indices.min().item(), y_indices.max().item()
                    if x_min >= x_max or y_min >= y_max:
                        continue
                    # 确保坐标在图像范围内
                    x_min = max(0, min(x_min, w-1))
                    y_min = max(0, min(y_min, h-1))
                    x_max = max(0, min(x_max, w-1))
                    y_max = max(0, min(y_max, h-1))
                    # 确保边界框面积足够大
                    if (x_max - x_min) * (y_max - y_min) > min_area:
                        sample_bboxes.append([x_min, y_min, x_max, y_max, c])
        bboxes.append(sample_bboxes)
    
    # 恢复原始日志级别
    python_logging.root.setLevel(original_level)
    return bboxes

# 可视化SAM2和DDNet预测结果的对比
def visualize_predictions(writer, iter_num, image, ddnet_pred, sam2_pred, gt=None):
    """可视化预测结果对比"""
    # 设置日志级别为ERROR以抑制调试输出
    original_level = python_logging.root.level
    python_logging.root.setLevel(python_logging.ERROR)
    
    try:
        writer.add_image('sam2/Image', image, iter_num)
        writer.add_image('sam2/DDNet_Pred', ddnet_pred * 50, iter_num)
        writer.add_image('sam2/SAM2_Pred', sam2_pred * 50, iter_num)
        if gt is not None:
            writer.add_image('sam2/GroundTruth', gt * 50, iter_num)
    finally:
        # 确保在任何情况下都恢复日志级别
        python_logging.root.setLevel(original_level)

def train(args, snapshot_path):
    base_lr = args.base_lr
    labeled_bs = args.labeled_bs
    num_classes = args.num_classes
    max_iterations = args.max_iterations

    def create_model(ema=False):
        model = net_factory(net_type=args.model, in_chns=1, class_num=num_classes)
        os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
        model = model.cuda()
        if ema:
            for param in model.parameters():
                param.detach_()
        return model

    def create_emamodel(ema=False):
        model = net_factory(net_type=args.model, in_chns=1, class_num=num_classes)
        os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
        model = model.cuda()
        if ema:
            for param in model.parameters():
                param.detach_()
        return model

    # 加载SAM2模型
    logging.info(f"Loading SAM2 model from local file: {args.sam2_weight}")
    try:
        # 根据模型ID确定配置文件
        model_type = "sam2.1_hiera_tiny"  # 默认为tiny模型
        if "small" in args.sam2_model_id:
            model_type = "sam2.1_hiera_s"
        elif "base" in args.sam2_model_id:
            model_type = "sam2.1_hiera_b+"
        elif "large" in args.sam2_model_id:
            model_type = "sam2.1_hiera_l"
            
        config_file = f"configs/sam2.1/{model_type}.yaml"
        
        # 修改日志级别以彻底抑制调试输出
        original_level = python_logging.root.level
        python_logging.root.setLevel(python_logging.ERROR)
        
        # 彻底禁用所有相关模块的日志
        for logger_name in ["sam2", "matplotlib", "PIL", "torch", "numpy"]:
            logger = python_logging.getLogger(logger_name)
            logger.setLevel(python_logging.ERROR)
            logger.propagate = False
        
        # 配置适配器参数
        hydra_overrides = []
        if args.use_adapter:
            # 使用适配器模式
            hydra_overrides = [
                f"++model.image_encoder.use_adapter=true",
                f"++model.image_encoder.adapter_scale_factor={args.adapter_scale_factor}",
                f"++model.image_encoder.img_size={args.adapter_img_size}",
                f"++model.image_encoder.patch_size={args.adapter_patch_size}"
            ]
            logging.info(f"启用SAM2适配器模式，配置: {hydra_overrides}")
        
        # 使用build_sam2函数加载本地SAM2模型
        sam2_base_model = build_sam2(
            config_file=config_file,  # 配置文件路径
            ckpt_path=args.sam2_weight,  # 本地权重文件路径
            device="cuda",
            hydra_overrides_extra=hydra_overrides  # 传递适配器配置
        )
        
        # 检查适配器状态
        has_adapter = hasattr(sam2_base_model.image_encoder, 'use_adapter') and sam2_base_model.image_encoder.use_adapter
        has_prompt_gen = hasattr(sam2_base_model.image_encoder, 'prompt_generator')
        
        # 记录适配器信息
        if args.use_adapter:
            # 检查适配器是否成功加载
            if has_adapter and has_prompt_gen:
                pg = sam2_base_model.image_encoder.prompt_generator
                logging.info(f"SAM2适配器成功加载！")
                logging.info(f"适配器参数: scale_factor={pg.scale_factor}, embed_dim={pg.embed_dim}")
                
                # 添加对比测试：使用print输出相同内容
                print(f"【PRINT输出】SAM2适配器成功加载！")
                print(f"【PRINT输出】适配器参数: scale_factor={pg.scale_factor}, embed_dim={pg.embed_dim}")
                
                # 统计适配器参数数量
                total_params = sum(p.numel() for p in sam2_base_model.parameters())
                adapter_params = sum(p.numel() for n, p in sam2_base_model.named_parameters() 
                                if 'prompt_generator' in n)
                logging.info(f"SAM2总参数数量: {total_params:,}")
                logging.info(f"适配器参数数量: {adapter_params:,} ({adapter_params/total_params*100:.2f}%)")
                
                # 设置SAM2适配器参数为可训练，冻结其他参数
                for name, param in sam2_base_model.named_parameters():
                    if 'prompt_generator' in name:
                        param.requires_grad = True  # 适配器参数设为可训练
                        logging.info(f"设置参数为可训练: {name}")
                    else:
                        param.requires_grad = False  # 其他参数冻结
                
                # 验证哪些参数可训练
                trainable_params = sum(p.numel() for p in sam2_base_model.parameters() if p.requires_grad)
                logging.info(f"SAM2可训练参数数量: {trainable_params:,} (应该接近适配器参数数量)")
            else:
                logging.warning(f"SAM2适配器加载失败！请检查配置和权重文件。")
        
        # 将基础模型包装成预测器
        sam2_model = SilentSAM2ImagePredictor(  # 使用静默版本的预测器
            sam_model=sam2_base_model,
            mask_threshold=0.0,
            max_hole_area=0.0,
            max_sprinkle_area=0.0
        )
        
        # 在其后添加对ensure_adapter_trainable函数的调用
        if 'ensure_adapter_trainable' in globals():
            sam2_model = ensure_adapter_trainable(sam2_model)
        
        # 恢复原始日志级别
        python_logging.root.setLevel(original_level)
        
        # 使用固定的图像尺寸，SAM2默认使用512x512的输入
        image_size = 512  # SAM2默认图像尺寸
        # 初始化transform - 使用正确的参数
        sam2_transform = SAM2Transforms(
            resolution=image_size,
            mask_threshold=0.0,
            max_hole_area=0.0,
            max_sprinkle_area=0.0
        )
        logging.info(f"SAM2 model loaded successfully")
    except Exception as e:
        logging.error(f"Failed to load SAM2 model: {e}")
        sam2_model = None
        sam2_transform = None

    model = create_model()

    # 创建EMA模型（如果启用）
    ema_model = None
    if args.use_ema_teacher:
        ema_model = create_emamodel(ema=True)
        logging.info("EMA教师模型已创建")

    def worker_init_fn(worker_id):
        random.seed(args.seed + worker_id)

    # 选择数据集类，支持有序切片和随机切片
    if args.use_ordered_slices:
        logging.info("Using ordered slices dataset")
        from dataloaders.dataset_3slices_ordered import OrderedTemporalBaseDataSets as DatasetClass
        from dataloaders.dataset_3slices_ordered import TemporalRandomGenerator, TwoStreamBatchSampler
    else:
        logging.info("Using random slices dataset")
        from dataloaders.dataset_3slices import TemporalBaseDataSets as DatasetClass
        from dataloaders.dataset_3slices import TemporalRandomGenerator, TwoStreamBatchSampler

    db_train = DatasetClass(base_dir=args.root_path,
                            split="train",
                            num=None,
                            transform=transforms.Compose([
                                TemporalRandomGenerator(args.patch_size)
                            ]))
    db_val = DatasetClass(base_dir=args.root_path, split="val")
    total_slices = len(db_train)

    labeled_slice = patients_to_slices(args.root_path, args.labeled_num)
    print("Total silices is: {}, labeled slices is: {}".format(total_slices, labeled_slice))
    labeled_idxs = list(range(0, labeled_slice))
    unlabeled_idxs = list(range(labeled_slice, total_slices))
    batch_sampler = TwoStreamBatchSampler(labeled_idxs, unlabeled_idxs, args.batch_size,
                                          args.batch_size - args.labeled_bs)

    trainloader = DataLoader(db_train, batch_sampler=batch_sampler, num_workers=4, pin_memory=True,
                             worker_init_fn=worker_init_fn)
    model.train()

    valloader = DataLoader(db_val, batch_size=1, shuffle=False, num_workers=1)

    optimizer = optim.SGD(model.parameters(), lr=base_lr, momentum=0.9, weight_decay=0.0001)
    
    # 创建SAM2适配器参数的优化器（如果启用了适配器）
    sam2_optimizer = None
    if args.use_adapter and sam2_model is not None:
        print(f"【调试】适配器启用: {args.use_adapter}, SAM2模型已加载")
        print(f"【调试】SAM2模型类型: {type(sam2_model)}")
        print(f"【调试】是否有sam_model属性: {hasattr(sam2_model, 'sam_model')}")
        
        if hasattr(sam2_model, 'sam_model'):
            # 检查适配器参数
            print(f"【调试】检查sam_model的参数名称:")
            for name, _ in sam2_model.sam_model.named_parameters():
                if 'prompt_generator' in name:
                    print(f"【调试】找到适配器参数: {name}")
            
            # 仅优化适配器参数，学习率设为基础学习率的0.1倍（可以调整）
            adapter_params = [p for n, p in sam2_model.sam_model.named_parameters() 
                            if 'prompt_generator' in n and p.requires_grad]
            print(f"【调试】可训练的适配器参数数量: {len(adapter_params)}")
            
            if adapter_params:
                sam2_optimizer = optim.Adam(adapter_params, lr=base_lr * 0.1)
                logging.info(f"创建SAM2适配器优化器，参数数量: {len(adapter_params)}")
                print(f"【调试】成功创建SAM2适配器优化器")
            else:
                print(f"【调试】没有找到可训练的适配器参数")
        else:
            print(f"【调试】SAM2模型没有sam_model属性")

    ce_loss = CrossEntropyLoss()

    mse_criterion = losses.mse_loss

    dice_loss = losses.DiceLoss(n_classes=num_classes)
    writer = SummaryWriter(snapshot_path + '/log')
    logging.info("{} iterations per epoch".format(len(trainloader)))
    iter_num = 0
    max_epoch = max_iterations // len(trainloader) + 1
    best_performance = 0.0
    iterator = tqdm(range(max_epoch), ncols=70)

    for _ in iterator:
        for _, sampled_batch in enumerate(trainloader):
            # 获取当前帧、前一帧和后一帧的图像数据
            volume_batch, label_batch = sampled_batch['image'], sampled_batch['label']
            prev_volume_batch, next_volume_batch = sampled_batch['prev_image'], sampled_batch['next_image']
            
            # 将所有数据移动到CUDA设备
            volume_batch, label_batch = volume_batch.cuda(), label_batch.cuda()
            prev_volume_batch, next_volume_batch = prev_volume_batch.cuda(), next_volume_batch.cuda()
            
            # 分离有标签数据和无标签数据
            unlabeled_volume_batch = volume_batch[labeled_bs:]
            unlabeled_prev_volume = prev_volume_batch[labeled_bs:]
            unlabeled_next_volume = next_volume_batch[labeled_bs:]

            # 将三帧图像序列输入到模型中
            outputs, deep_out2 = model(prev_volume_batch, volume_batch, next_volume_batch)

            loss_seg_dice = 0

            output1_soft = F.softmax(outputs[0], dim=1)    # [b,4,256,256]
            output2_soft = F.softmax(outputs[1], dim=1)    # [b,4,256,256]

            deep_out2_soft1 = F.softmax(deep_out2[0], dim=1)
            deep_out2_soft2 = F.softmax(deep_out2[1], dim=1)
            deep_out2_soft3 = F.softmax(deep_out2[2], dim=1)
            deep_out2_soft4 = F.softmax(deep_out2[3], dim=1)

            mask_high = (output1_soft >= 0.80) & (output2_soft >= 0.80) # [b,4,256,256]
            mask_mid = ((output1_soft > 0.80) & (output2_soft < 0.80)) | ((output1_soft < 0.80) & (output2_soft > 0.80))
            mask_low = (output1_soft < 0.80) & (output2_soft < 0.80)

            high_output1 = torch.mul(mask_high, outputs[0])    # [b,4,256,256]
            high_output2 = torch.mul(mask_high, outputs[1])    # [b,4,256,256]
            high_output1_soft = torch.mul(mask_high, output1_soft)  # [b,4,256,256]
            high_output2_soft = torch.mul(mask_high, output2_soft)  # [b,4,256,256]

            mid_output1 = torch.mul(mask_mid, outputs[0])
            mid_output2 = torch.mul(mask_mid, outputs[1])

            low_output1 = torch.mul(mask_low, outputs[0])
            low_output2 = torch.mul(mask_low, outputs[1])

            loss_seg_dice += dice_loss(output1_soft[:labeled_bs, ...], label_batch[:labeled_bs].unsqueeze(1))
            loss_seg_dice += dice_loss(output2_soft[:labeled_bs, ...], label_batch[:labeled_bs].unsqueeze(1))

            loss_deep_sup2_1 = dice_loss(deep_out2_soft1[:labeled_bs, ...], label_batch[:labeled_bs].unsqueeze(1))
            loss_deep_sup2_2 = dice_loss(deep_out2_soft2[:labeled_bs, ...], label_batch[:labeled_bs].unsqueeze(1))
            loss_deep_sup2_3 = dice_loss(deep_out2_soft3[:labeled_bs, ...], label_batch[:labeled_bs].unsqueeze(1))
            loss_deep_sup2_4 = dice_loss(deep_out2_soft4[:labeled_bs, ...], label_batch[:labeled_bs].unsqueeze(1))

            loss_deep_sup = (loss_deep_sup2_1+loss_deep_sup2_2+loss_deep_sup2_3+loss_deep_sup2_4)/4   # 转置卷积

            pseudo_high_output1 = torch.argmax(high_output1_soft[labeled_bs:], dim=1)
            pseudo_high_output2 = torch.argmax(high_output2_soft[labeled_bs:], dim=1)

            pseudo_supervision = 0

            pseudo_supervision += ce_loss(high_output1[labeled_bs:], pseudo_high_output2.long().detach())
            pseudo_supervision += ce_loss(high_output2[labeled_bs:], pseudo_high_output1.long().detach())

            loss_mm = dice1_loss(F.softmax(mid_output1[labeled_bs:] / 2, dim=1), F.softmax(mid_output2[labeled_bs:] / 2, dim=1))

            low_loss = mse_criterion(F.softmax(low_output1[labeled_bs:] / 0.5, dim=1), F.softmax(low_output2[labeled_bs:] / 0.5, dim=1))

            # 初始化教师模型相关损失
            loss_sam2 = 0
            loss_u = 0  # 教师模型的无标签损失
            sam2_adapter_loss = 0  # 适配器训练损失
            ema_loss = 0  # EMA教师损失

            # 判断当前使用哪个教师模型
            use_ema_as_teacher = (iter_num < args.teacher_switch_iter) and (ema_model is not None) and args.use_ema_teacher
            use_sam2_as_teacher = (iter_num >= args.teacher_switch_iter) and (sam2_model is not None)

            # 记录当前教师模型状态
            if iter_num % 1000 == 0:
                if use_ema_as_teacher:
                    logging.info(f"当前使用EMA作为教师模型 (迭代 {iter_num} < {args.teacher_switch_iter})")
                elif use_sam2_as_teacher:
                    logging.info(f"当前使用SAM2作为教师模型 (迭代 {iter_num} >= {args.teacher_switch_iter})")
                else:
                    logging.info(f"当前没有启用教师模型 (迭代 {iter_num})")

            # EMA教师模型逻辑（前期阶段）
            if use_ema_as_teacher:
                with torch.no_grad():
                    ema_outputs, _ = ema_model(unlabeled_prev_volume, unlabeled_volume_batch, unlabeled_next_volume)
                    ema_out = ema_outputs[0]  # 只取第一个输出
                    ema_out_soft = F.softmax(ema_out, dim=1)

                    # 生成EMA伪标签
                    ema_pseudo = torch.argmax(ema_out_soft, dim=1)

                    # 计算EMA教师损失
                    loss_u = ce_loss((outputs[0][labeled_bs:] + outputs[1][labeled_bs:]) * 0.5, ema_pseudo.long().detach())
                    ema_loss = loss_u

                    if iter_num % 5 == 0:
                        logging.info(f'EMA教师损失: {loss_u.item()}')

            # SAM2相关处理（适配器训练在整个过程中进行，教师功能仅在后期启用）
            if sam2_model is not None:
                # 如果启用了适配器训练
                if args.use_adapter and sam2_optimizer is not None and labeled_bs > 0:
                    try:
                        # 使用有标签数据训练适配器
                        sam2_adapter_loss_total = 0.0
                        processed_samples = 0
                        
                        # 从批次中取前labeled_bs个样本进行适配器训练
                        for idx in range(min(labeled_bs, 4)):  # 限制最多处理4个样本，避免内存溢出
                            try:
                                # 准备输入图像
                                image = volume_batch[idx].cpu().numpy()[0]  # [1, H, W] -> [H, W]
                                # 获取真实标签
                                true_mask = label_batch[idx].cpu().numpy()
                                
                                # 使用专用训练函数进行适配器训练
                                sample_loss = train_sam_adapter(sam2_model, image, true_mask, num_classes)
                                sam2_adapter_loss_total += sample_loss
                                processed_samples += 1
                                
                            except Exception as e:
                                continue
                        
                        # 如果有成功处理的样本，进行梯度更新
                        if processed_samples > 0:
                            # 计算平均损失
                            sam2_adapter_loss = sam2_adapter_loss_total / processed_samples
                            
                            # 优化SAM2适配器参数
                            sam2_optimizer.zero_grad()
                            sam2_adapter_loss.backward()
                            sam2_optimizer.step()
                            
                            # 记录适配器训练信息
                            if iter_num % 5 == 0:  # 每5次迭代输出适配器训练状态
                                logging.info(f'SAM2适配器训练成功，平均损失: {sam2_adapter_loss.item()}')
                    
                    except Exception as e:
                        logging.error(f"SAM2适配器训练过程出错: {e}")
                
                # SAM2生成无标签伪标签（教师模型功能，仅在后期阶段启用）
                if use_sam2_as_teacher and iter_num % args.sam2_frequency == 0:  # 根据频率参数决定是否应用SAM2
                    try:
                        # 使用DDNet的高置信度预测作为SAM2的提示
                        with torch.no_grad():
                            # 仅处理无标签数据
                            unlabeled_pred = (output1_soft[labeled_bs:] + output2_soft[labeled_bs:]) * 0.5
                            
                            # 根据迭代次数选择提示方式
                            use_mask_prompt = iter_num > 10000
                            
                            sam2_pseudo_labels = []
                            
                            # 逐个处理批次中的样本
                            for idx in range(unlabeled_pred.shape[0]):
                                try:
                                    # 准备SAM2输入
                                    image = volume_batch[labeled_bs + idx].cpu().numpy()[0]  # [1, H, W] -> [H, W]
                                    image = (image * 255).astype(np.uint8)  # 归一化到0-255
                                    image = np.repeat(image[..., np.newaxis], 3, axis=2)  # [H, W] -> [H, W, 3]
                                    image = image.astype(np.float32) / 255.0
                                    
                                    # 设置图像
                                    sam2_model.set_image(image)
                                    
                                    if use_mask_prompt:
                                        # 使用掩码提示
                                        all_masks = []
                                        for c in range(1, num_classes):  # 跳过背景类
                                            mask = unlabeled_pred[idx, c] > args.sam2_confidence
                                            if mask.sum() > 100:  # 确保有足够的像素
                                                mask_np = mask.cpu().numpy().astype(np.uint8)
                                                
                                                # 获取SAM2预测
                                                sam_masks, _, _ = sam2_model.predict(
                                                    point_coords=None,
                                                    point_labels=None,
                                                    mask_input=mask_np,
                                                    multimask_output=False
                                                )
                                                
                                                if isinstance(sam_masks, np.ndarray):
                                                    sam_masks = torch.from_numpy(sam_masks).float()
                                                
                                                mask = F.interpolate(
                                                    sam_masks.unsqueeze(0).unsqueeze(0), 
                                                    size=unlabeled_pred.shape[2:], 
                                                    mode='bilinear', 
                                                    align_corners=False
                                                )[0, 0]
                                                
                                                class_mask = torch.zeros((num_classes, *unlabeled_pred.shape[2:]), device=mask.device)
                                                class_mask[c] = mask
                                                all_masks.append(class_mask)
                                        
                                        if all_masks:
                                            combined_mask = torch.stack(all_masks).sum(dim=0)
                                            combined_mask = F.normalize(combined_mask, p=1, dim=0)
                                            sam2_pseudo_labels.append(combined_mask)
                                        else:
                                            sam2_pseudo_labels.append(torch.zeros((num_classes, *unlabeled_pred.shape[2:]), device='cuda'))
                                    
                                    else:
                                        # 使用边界框提示
                                        bboxes = generate_bbox_from_pred(unlabeled_pred[idx:idx+1], args.sam2_confidence)
                                        
                                        if len(bboxes[0]) == 0:
                                            sam2_pseudo_labels.append(torch.zeros((num_classes, *unlabeled_pred.shape[2:]), device='cuda'))
                                            continue
                                            
                                        masks = []
                                        for box in bboxes[0]:
                                            x1, y1, x2, y2, class_id = box
                                            input_box = np.array([x1, y1, x2, y2])
                                            
                                            try:
                                                pred_masks, _, _ = sam2_model.predict(
                                                    point_coords=None,
                                                    point_labels=None,
                                                    box=input_box,
                                                    multimask_output=False,
                                                )
                                                
                                                if isinstance(pred_masks, np.ndarray):
                                                    pred_masks = torch.from_numpy(pred_masks).float()
                                                
                                                mask = F.interpolate(
                                                    pred_masks.unsqueeze(0).unsqueeze(0), 
                                                    size=unlabeled_pred.shape[2:], 
                                                    mode='bilinear', 
                                                    align_corners=False
                                                )[0, 0]
                                                
                                                class_mask = torch.zeros((num_classes, *unlabeled_pred.shape[2:]), device=mask.device)
                                                class_mask[class_id] = mask
                                                masks.append(class_mask)
                                                
                                            except Exception as e:
                                                continue
                                        
                                        if masks:
                                            combined_mask = torch.stack(masks).sum(dim=0)
                                            combined_mask = F.normalize(combined_mask, p=1, dim=0)
                                            sam2_pseudo_labels.append(combined_mask)
                                        else:
                                            sam2_pseudo_labels.append(torch.zeros((num_classes, *unlabeled_pred.shape[2:]), device='cuda'))
                                
                                except Exception as e:
                                    sam2_pseudo_labels.append(torch.zeros((num_classes, *unlabeled_pred.shape[2:]), device='cuda'))
                                    continue
                            
                            # 将SAM2伪标签用作教师指导
                            if sam2_pseudo_labels:
                                sam2_pseudo_batch = torch.stack(sam2_pseudo_labels)
                                sam2_pseudo = torch.argmax(sam2_pseudo_batch, dim=1)
                                
                                # 使用SAM2伪标签作为无标签损失的目标（替代原来的EMA损失）
                                loss_u = ce_loss((outputs[0][labeled_bs:] + outputs[1][labeled_bs:]) * 0.5, sam2_pseudo.long())
                                
                                # 可选：保留SAM2特定损失（可以根据需要调整权重）
                                loss_sam2 = loss_u  # 这里统一为教师损失
                                
                                # 可视化SAM2伪标签
                                if iter_num % 1000 == 0:
                                    ddnet_pred = torch.argmax((outputs[0][labeled_bs] + outputs[1][labeled_bs]) * 0.5, dim=0, keepdim=True)
                                    sam2_pred = sam2_pseudo[0].unsqueeze(0)
                                    visualize_predictions(
                                        writer, 
                                        iter_num,
                                        volume_batch[labeled_bs],
                                        ddnet_pred, 
                                        sam2_pred
                                    )
                                
                                if iter_num % 5 == 0:  # 每5次迭代输出教师损失
                                    logging.info(f'SAM2教师损失: {loss_u.item()}')
                    
                    except Exception as e:
                        logging.error(f"使用SAM2生成教师伪标签时发生错误: {e}")
                        if use_sam2_as_teacher:
                            loss_u = 0
                            loss_sam2 = 0
                else:
                    # SAM2不作为教师模型时，确保相关损失为0
                    if not use_sam2_as_teacher:
                        loss_sam2 = 0
            else:
                if use_sam2_as_teacher:
                    logging.warning("SAM2模型未加载，无法使用教师指导")
                    loss_u = 0
                    loss_sam2 = 0
            
            consistency_weight = get_current_consistency_weight(iter_num // 150)
            supervised_loss = loss_seg_dice
            
            # 合并所有损失，根据迭代次数使用不同的教师模型
            # 前期阶段：EMA作为教师模型，SAM2仅训练适配器
            # 后期阶段：SAM2作为教师模型，EMA停止工作
            loss = supervised_loss + loss_deep_sup + consistency_weight * pseudo_supervision + \
                   (1 - consistency_weight) * loss_mm + 2 * low_loss + consistency_weight * loss_u + \
                   consistency_weight * args.sam2_weight_loss * loss_sam2  # 动态教师损失

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # 更新EMA模型（如果启用且在前期阶段）
            if use_ema_as_teacher and ema_model is not None:
                update_ema_variables(model, ema_model, args.ema_decay, iter_num)

            iter_num = iter_num + 1
            
            # 记录所有损失信息到日志中
            teacher_type = "EMA" if use_ema_as_teacher else ("SAM2" if use_sam2_as_teacher else "None")
            logging.info('iteration %d : loss : %03f, supervised_loss: %03f, loss_deep_sup: %03f, pseudo_supervision: %03f, loss_kd: %03f, low_loss: %03f, teacher_loss: %03f, teacher_type: %s, ema_loss: %03f, sam2_loss: %03f'
                          % (
            iter_num, loss, supervised_loss, loss_deep_sup, pseudo_supervision, loss_mm, low_loss, loss_u, teacher_type, ema_loss, loss_sam2))

            if iter_num % 20 == 0:
                # 只可视化中心帧图像
                image = volume_batch[1, 0:1, :, :]
                writer.add_image('train/Image', image, iter_num)
                output = torch.argmax(torch.softmax(outputs[0], dim=1), dim=1, keepdim=True)
                writer.add_image('train/Prediction', output[1, ...] * 50, iter_num)
                labs = label_batch[1, ...].unsqueeze(0) * 50
                writer.add_image('train/GroundTruth', labs, iter_num)
                
                # 额外可视化前后帧图像
                prev_image = prev_volume_batch[1, 0:1, :, :]
                next_image = next_volume_batch[1, 0:1, :, :]
                writer.add_image('train/PrevImage', prev_image, iter_num)
                writer.add_image('train/NextImage', next_image, iter_num)

            if iter_num > 0 and iter_num % 200 == 0:
                model.eval()
                metric_list = 0.0
                for _, sampled_batch in enumerate(valloader):
                    # 获取验证数据的三帧序列
                    val_image = sampled_batch["image"]
                    val_prev_image = sampled_batch["prev_image"]
                    val_next_image = sampled_batch["next_image"]
                    val_label = sampled_batch["label"]
                    
                    # 调用修改后的验证函数，传入三帧序列
                    metric_i = val.test_temporal_volume(
                        (val_prev_image, val_image, val_next_image), 
                        val_label, 
                        model, 
                        classes=num_classes
                    )
                    metric_list += np.array(metric_i)
                metric_list = metric_list / len(db_val)
                for class_i in range(num_classes - 1):
                    writer.add_scalar('info/val_{}_dice'.format(class_i + 1), metric_list[class_i, 0], iter_num)
                    writer.add_scalar('info/val_{}_hd95'.format(class_i + 1), metric_list[class_i, 1], iter_num)

                performance = np.mean(metric_list, axis=0)[0]

                mean_hd95 = np.mean(metric_list, axis=0)[1]
                writer.add_scalar('info/val_mean_dice', performance, iter_num)
                writer.add_scalar('info/val_mean_hd95', mean_hd95, iter_num)

                if performance > best_performance:
                    best_performance = performance
                    save_mode_path = os.path.join(snapshot_path,
                                                  'iter_{}_dice_{}.pth'.format(iter_num, round(best_performance, 4)))
                    save_best_path = os.path.join(snapshot_path, '{}_best_model.pth'.format(args.model))
                    torch.save(model.state_dict(), save_mode_path)
                    torch.save(model.state_dict(), save_best_path)

                logging.info('iteration %d : mean_dice : %f mean_hd95 : %f' % (iter_num, performance, mean_hd95))
                model.train()

            # 保存SAM2适配器模型
            if iter_num % 1000 == 0 and args.use_adapter and sam2_model is not None:
                try:
                    # 创建适配器模型保存目录
                    sam2_adapter_dir = os.path.join(snapshot_path, 'sam2_adapter')
                    os.makedirs(sam2_adapter_dir, exist_ok=True)
                    
                    # 保存当前迭代的适配器参数
                    adapter_save_path = os.path.join(sam2_adapter_dir, f'adapter_iter_{iter_num}.pth')
                    
                    # 只保存适配器参数
                    adapter_state_dict = {
                        k: v for k, v in sam2_model.sam_model.state_dict().items() 
                        if 'prompt_generator' in k
                    }
                    torch.save({
                        'iter': iter_num,
                        'adapter_state_dict': adapter_state_dict,
                    }, adapter_save_path)
                    
                    logging.info(f"已保存SAM2适配器参数到 {adapter_save_path}")
                except Exception as e:
                    logging.error(f"保存SAM2适配器参数时出错: {e}")

            if iter_num >= max_iterations:
                break
        if iter_num >= max_iterations:
            iterator.close()
            break
    
    # 保存最终的SAM2适配器模型
    if args.use_adapter and sam2_model is not None:
        try:
            # 创建适配器模型保存目录
            sam2_adapter_dir = os.path.join(snapshot_path, 'sam2_adapter')
            os.makedirs(sam2_adapter_dir, exist_ok=True)
            
            # 保存最终的适配器参数
            adapter_save_path = os.path.join(sam2_adapter_dir, 'adapter_final.pth')
            
            # 只保存适配器参数
            adapter_state_dict = {
                k: v for k, v in sam2_model.sam_model.state_dict().items() 
                if 'prompt_generator' in k
            }
            torch.save({
                'iter': iter_num,
                'adapter_state_dict': adapter_state_dict,
            }, adapter_save_path)
            
            logging.info(f"已保存最终SAM2适配器参数到 {adapter_save_path}")
        except Exception as e:
            logging.error(f"保存最终SAM2适配器参数时出错: {e}")
    
    writer.close()
    return "Training Finished!"

# 在dice1_loss函数后添加train_sam_adapter函数
def train_sam_adapter(sam2_model, image, true_mask, num_classes):
    """专用于训练SAM2适配器的函数，避开no_grad装饰器"""
    
    # 导入必要的模块
    import torch.nn as nn
    
    # 获取图像编码器引用
    image_encoder = sam2_model.sam_model.image_encoder
    
    # 保存原始训练状态
    orig_training = image_encoder.training
    
    # 确保适配器模块处于训练模式
    image_encoder.train()
    
    # 获取设备信息 - 从参数中获取而不是访问device属性
    device = next(image_encoder.parameters()).device
    # print(f"【调试】使用设备: {device}")
    
    # 准备输入
    # 将numpy图像转换为合适的tensor
    if isinstance(image, np.ndarray):
        # [H, W] -> [1, 3, H, W]
        if len(image.shape) == 2:
            img_tensor = torch.from_numpy(image).float().unsqueeze(0).unsqueeze(0)  # 添加通道和批次维度
            img_tensor = img_tensor.repeat(1, 3, 1, 1)  # 复制到三个通道
        else:
            # [H, W, C] -> [1, 3, H, W]
            img_tensor = torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0)
    else:
        # 假设已经是tensor: [1, 1, H, W]
        img_tensor = image.repeat(1, 3, 1, 1) if image.shape[1] == 1 else image
    
    # 确保图像在正确的设备上
    img_tensor = img_tensor.to(device)
    
    # 明确启用梯度计算
    with torch.enable_grad():
        # 直接通过图像编码器前向传播
        output = image_encoder(img_tensor)
        
        # 如果真实掩码是numpy数组，转换为tensor
        if isinstance(true_mask, np.ndarray):
            true_mask_tensor = torch.from_numpy(true_mask).long()
        else:
            true_mask_tensor = true_mask.long()
        
        # 确保真实掩码在正确的设备上
        true_mask_tensor = true_mask_tensor.to(device)
        
        # 使用一个临时的预测头从特征生成掩码
        if not hasattr(image_encoder, 'adapter_pred_head'):
            # 创建一个简单的预测头
            adapter_pred_head = nn.Conv2d(
                in_channels=output['vision_features'].size(1),  
                out_channels=num_classes,
                kernel_size=1
            ).to(device)
            image_encoder.adapter_pred_head = adapter_pred_head
        
        # 生成掩码预测
        mask_logits = image_encoder.adapter_pred_head(output['vision_features'])
        
        # 调整大小到真实掩码尺寸
        mask_size = true_mask_tensor.shape
        mask_pred = F.interpolate(
            mask_logits,
            size=(mask_size[0], mask_size[1]),
            mode='bilinear',
            align_corners=False
        )
        
        # 计算损失 - 修复维度不匹配问题
        loss_fn = torch.nn.CrossEntropyLoss()
        
        # # 调试输出形状信息
        # print(f"【调试】mask_pred形状: {mask_pred.shape}")
        # print(f"【调试】true_mask_tensor形状: {true_mask_tensor.shape}")
        
        # CrossEntropyLoss需要输入形状为[B,C,H,W]，目标形状为[B,H,W]
        # 确保mask_pred是[1,num_classes,H,W]格式
        if len(mask_pred.shape) == 3:  # [C,H,W]
            mask_pred = mask_pred.unsqueeze(0)  # [1,C,H,W]
        
        # 确保true_mask_tensor是[1,H,W]格式
        if len(true_mask_tensor.shape) == 2:  # [H,W]
            true_mask_tensor = true_mask_tensor.unsqueeze(0)  # [1,H,W]
        
        # 确保目标不包含类别维度
        if len(true_mask_tensor.shape) == 4:  # [1,C,H,W]
            true_mask_tensor = true_mask_tensor.squeeze(1)  # [1,H,W]
        
        # print(f"【调试】调整后mask_pred形状: {mask_pred.shape}")
        # print(f"【调试】调整后true_mask_tensor形状: {true_mask_tensor.shape}")
        
        loss = loss_fn(mask_pred, true_mask_tensor)
        
    # 恢复原始训练状态
    image_encoder.training = orig_training
    
    return loss

# 在SAM2模型加载部分添加代码，确保适配器参数被正确标记为可训练
# 在这一行之后: sam2_model = SilentSAM2ImagePredictor(...)
def ensure_adapter_trainable(sam2_model):
    """确保SAM2适配器参数可训练"""
    if hasattr(sam2_model, 'sam_model') and hasattr(sam2_model.sam_model, 'image_encoder'):
        image_encoder = sam2_model.sam_model.image_encoder
        if hasattr(image_encoder, 'use_adapter') and image_encoder.use_adapter:
            print("设置SAM2适配器为训练模式...")
            # 设置适配器为训练模式
            for name, module in image_encoder.named_modules():
                if 'prompt_generator' in name:
                    module.train()
                    print(f"【设置模块为训练模式】: {name}")
            
            # 确保适配器参数可训练
            for name, param in image_encoder.named_parameters():
                if 'prompt_generator' in name:
                    param.requires_grad = True
            
            print("SAM2适配器参数已设置为可训练")
    return sam2_model

if __name__ == "__main__":
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建快照路径
    snapshot_path = "../model/ACDC_{}_{}_{}_labeled".format(args.model, args.exp, args.labeled_num)
    if not os.path.exists(snapshot_path):
        os.makedirs(snapshot_path)
    if os.path.exists(snapshot_path + '/code'):
        shutil.rmtree(snapshot_path + '/code')
    shutil.copytree('../code/', snapshot_path + '/code', shutil.ignore_patterns(['.git', '__pycache__']))
    
    # 定义日志文件路径
    log_file = snapshot_path + "/log.txt"
    
    # 所有输出(包括print和日志)都重定向到同一个文件
    tee = Tee(snapshot_path + "/all_output.txt", 'w')
    print(f"所有终端输出已重定向到: {snapshot_path}/all_output.txt")
    
    # 配置日志 - 同时输出到控制台和log.txt
    setup_early_logging(log_file)
    
    # 安装依赖
    install_dependencies()
    
    # 检查并设置SAM2权重文件路径
    args.sam2_weight = check_sam2_weights(args.sam2_model_id, bool(args.use_adapter))
    
    if args.deterministic:
        cudnn.benchmark = False
        cudnn.deterministic = True
        random.seed(args.seed)
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        torch.cuda.manual_seed(args.seed)

    # 已经在上面配置过日志，这里不需要重复配置
    # 但仍然记录一些基本信息
    logging.info("============= 训练开始 =============")
    logging.info(f"日志文件保存在: {log_file}")
    logging.info(str(args))

    # 训练中所有通过logging.info()的信息将同时记录到控制台和log.txt
    train(args, snapshot_path) 